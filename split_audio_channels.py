#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频双声道拆分程序
将双声道音频文件拆分为左右声道单独的文件
"""

import os
import glob
import numpy as np
from scipy.io import wavfile
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('split_log.txt', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def split_stereo_audio(input_file, output_dir=None):
    """
    拆分双声道音频为左右声道

    Args:
        input_file: 输入音频文件路径
        output_dir: 输出目录，如果为None则使用输入文件所在目录

    Returns:
        tuple: (left_channel_file, right_channel_file) 或 (None, None) 如果失败
    """
    try:
        # 加载音频文件
        logging.info(f"正在处理: {input_file}")
        sample_rate, audio_data = wavfile.read(input_file)

        # 检查音频数据的形状
        if len(audio_data.shape) == 1:
            logging.warning(f"文件是单声道: {input_file}")
            return None, None

        if audio_data.shape[1] != 2:
            logging.warning(f"文件不是双声道: {input_file} (声道数: {audio_data.shape[1]})")
            return None, None

        # 拆分左右声道
        left_channel = audio_data[:, 0]   # 左声道
        right_channel = audio_data[:, 1]  # 右声道

        # 生成输出文件名
        if output_dir is None:
            output_dir = os.path.dirname(input_file)

        base_name = os.path.splitext(os.path.basename(input_file))[0]
        left_file = os.path.join(output_dir, f"{base_name}_左声道.wav")
        right_file = os.path.join(output_dir, f"{base_name}_右声道.wav")

        # 保存左右声道
        wavfile.write(left_file, sample_rate, left_channel)
        wavfile.write(right_file, sample_rate, right_channel)

        logging.info(f"拆分完成:")
        logging.info(f"  左声道: {left_file}")
        logging.info(f"  右声道: {right_file}")

        return left_file, right_file

    except Exception as e:
        logging.error(f"处理文件失败 {input_file}: {e}")
        return None, None

def process_audio_folder(folder_path, output_folder="split_channels"):
    """
    处理文件夹中的所有WAV文件

    Args:
        folder_path: 音频文件夹路径
        output_folder: 输出文件夹路径
    """
    # 创建输出文件夹
    os.makedirs(output_folder, exist_ok=True)
    logging.info(f"拆分文件将保存到: {output_folder}")

    # 查找所有WAV文件
    wav_files = glob.glob(os.path.join(folder_path, "*.wav"))

    if not wav_files:
        logging.warning(f"在文件夹 {folder_path} 中没有找到WAV文件")
        return

    # 过滤掉已经拆分的文件
    original_files = [f for f in wav_files if "_左声道" not in f and "_右声道" not in f]

    logging.info(f"找到 {len(original_files)} 个原始WAV文件")

    success_count = 0
    failed_count = 0
    skipped_count = 0

    for i, wav_file in enumerate(original_files, 1):
        base_name = os.path.splitext(os.path.basename(wav_file))[0]
        left_file = os.path.join(output_folder, f"{base_name}_左声道.wav")
        right_file = os.path.join(output_folder, f"{base_name}_右声道.wav")

        # 检查是否已经拆分过
        if os.path.exists(left_file) and os.path.exists(right_file):
            logging.info(f"文件已拆分，跳过: {wav_file}")
            skipped_count += 1
            continue

        logging.info(f"进度: {i}/{len(original_files)} - 处理: {os.path.basename(wav_file)}")

        left_result, right_result = split_stereo_audio(wav_file, output_folder)

        if left_result and right_result:
            success_count += 1
        else:
            failed_count += 1

    # 输出统计信息
    logging.info(f"\n处理完成!")
    logging.info(f"成功拆分: {success_count} 个文件")
    logging.info(f"跳过文件: {skipped_count} 个文件")
    logging.info(f"处理失败: {failed_count} 个文件")
    logging.info(f"拆分文件保存在: {output_folder} 文件夹")

def main():
    """主函数"""
    audio_folder = "audio_files"
    
    if not os.path.exists(audio_folder):
        logging.error(f"音频文件夹不存在: {audio_folder}")
        return
    
    logging.info(f"开始处理音频文件夹: {audio_folder}")
    process_audio_folder(audio_folder)

if __name__ == "__main__":
    main()
