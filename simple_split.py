#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版音频双声道拆分程序
使用scipy.io.wavfile处理WAV文件
"""

import os
import glob
import numpy as np
from scipy.io import wavfile

def split_audio(input_file):
    """拆分双声道音频"""
    try:
        print(f"正在处理: {os.path.basename(input_file)}")

        # 读取WAV文件
        sample_rate, audio_data = wavfile.read(input_file)

        # 检查音频数据的形状
        if len(audio_data.shape) == 1:
            print(f"  跳过: 单声道文件")
            return False

        if audio_data.shape[1] != 2:
            print(f"  跳过: 不是双声道 (声道数: {audio_data.shape[1]})")
            return False

        # 拆分左右声道
        left_channel = audio_data[:, 0]   # 左声道
        right_channel = audio_data[:, 1]  # 右声道

        # 生成文件名
        base_name = os.path.splitext(input_file)[0]
        left_file = f"{base_name}_左声道.wav"
        right_file = f"{base_name}_右声道.wav"

        # 保存左右声道
        wavfile.write(left_file, sample_rate, left_channel)
        wavfile.write(right_file, sample_rate, right_channel)

        print(f"  完成: 生成左右声道文件")
        print(f"    左声道: {os.path.basename(left_file)}")
        print(f"    右声道: {os.path.basename(right_file)}")
        return True

    except Exception as e:
        print(f"  错误: {e}")
        return False

def main():
    audio_folder = "audio_files"
    
    # 查找所有WAV文件
    wav_files = glob.glob(os.path.join(audio_folder, "*.wav"))
    
    # 过滤掉已经拆分的文件
    original_files = [f for f in wav_files if "_左声道" not in f and "_右声道" not in f]
    
    print(f"找到 {len(original_files)} 个原始音频文件")
    
    success = 0
    for i, wav_file in enumerate(original_files, 1):
        print(f"\n进度: {i}/{len(original_files)}")
        if split_audio(wav_file):
            success += 1
    
    print(f"\n完成! 成功拆分 {success} 个文件")

if __name__ == "__main__":
    main()
