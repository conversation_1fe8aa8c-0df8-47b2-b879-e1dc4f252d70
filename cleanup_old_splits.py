#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理脚本：删除audio_files文件夹中的旧拆分文件
"""

import os
import glob

def cleanup_old_split_files():
    """清理audio_files文件夹中的旧拆分文件"""
    audio_folder = "audio_files"
    
    if not os.path.exists(audio_folder):
        print(f"文件夹不存在: {audio_folder}")
        return
    
    # 查找所有拆分文件
    left_files = glob.glob(os.path.join(audio_folder, "*_左声道.wav"))
    right_files = glob.glob(os.path.join(audio_folder, "*_右声道.wav"))
    
    all_split_files = left_files + right_files
    
    if not all_split_files:
        print("没有找到需要清理的拆分文件")
        return
    
    print(f"找到 {len(all_split_files)} 个旧的拆分文件:")
    for file in all_split_files:
        print(f"  - {os.path.basename(file)}")
    
    # 询问用户确认
    confirm = input("\n确认删除这些文件吗? (y/N): ").strip().lower()
    
    if confirm in ['y', 'yes']:
        deleted_count = 0
        for file in all_split_files:
            try:
                os.remove(file)
                print(f"已删除: {os.path.basename(file)}")
                deleted_count += 1
            except Exception as e:
                print(f"删除失败 {os.path.basename(file)}: {e}")
        
        print(f"\n清理完成! 共删除 {deleted_count} 个文件")
    else:
        print("取消清理操作")

if __name__ == "__main__":
    cleanup_old_split_files()
