#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频下载程序
从CSV文件中读取音频路径并下载到当前文件夹
"""

import csv
import os
import requests
import urllib.parse
from pathlib import Path
import time
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('download_log.txt', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def extract_filename_from_url(url):
    """从URL中提取文件名"""
    try:
        # 解析URL，获取路径部分
        parsed_url = urllib.parse.urlparse(url)
        path = parsed_url.path
        
        # 从路径中提取文件名
        filename = os.path.basename(path)
        
        # 如果文件名为空，使用默认名称
        if not filename:
            filename = "audio_file.wav"
            
        return filename
    except Exception as e:
        logging.error(f"提取文件名失败: {e}")
        return "audio_file.wav"

def download_audio(url, filename, max_retries=3):
    """下载音频文件"""
    for attempt in range(max_retries):
        try:
            logging.info(f"开始下载: {filename} (尝试 {attempt + 1}/{max_retries})")
            
            # 发送HTTP请求
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()
            
            # 保存文件
            with open(filename, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            # 检查文件大小
            file_size = os.path.getsize(filename)
            if file_size > 0:
                logging.info(f"下载成功: {filename} ({file_size} bytes)")
                return True
            else:
                logging.warning(f"下载的文件为空: {filename}")
                os.remove(filename)
                
        except requests.exceptions.RequestException as e:
            logging.error(f"下载失败 (尝试 {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2)  # 等待2秒后重试
            
        except Exception as e:
            logging.error(f"下载过程中发生错误: {e}")
            break
    
    return False

def read_csv_and_download(csv_file, download_folder="./audio_files"):
    """读取CSV文件并下载音频"""
    
    # 创建下载文件夹
    Path(download_folder).mkdir(exist_ok=True)
    
    success_count = 0
    failed_count = 0
    failed_files = []
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row_num, row in enumerate(reader, start=2):  # 从第2行开始（第1行是标题）
                try:
                    # 获取音频路径
                    audio_path = row.get('audio_path', '').strip()
                    record_file = row.get('record_file', '').strip()
                    company_name = row.get('company_name', '').strip()
                    
                    if not audio_path:
                        logging.warning(f"第{row_num}行: 音频路径为空")
                        continue
                    
                    # 生成文件名
                    original_filename = extract_filename_from_url(audio_path)
                    name, ext = os.path.splitext(original_filename)
                    
                    # 使用record_file作为文件名，如果没有则使用原始文件名
                    if record_file:
                        filename = f"{record_file}{ext}"
                    else:
                        filename = original_filename
                    
                    # 如果有公司名称，添加到文件名前缀
                    if company_name:
                        name_part, ext_part = os.path.splitext(filename)
                        filename = f"{company_name}_{name_part}{ext_part}"
                    
                    # 完整的文件路径
                    full_path = os.path.join(download_folder, filename)
                    
                    # 检查文件是否已存在
                    if os.path.exists(full_path):
                        logging.info(f"文件已存在，跳过: {filename}")
                        continue
                    
                    # 下载文件
                    if download_audio(audio_path, full_path):
                        success_count += 1
                        logging.info(f"进度: {success_count + failed_count}/{row_num - 1}")
                    else:
                        failed_count += 1
                        failed_files.append({
                            'row': row_num,
                            'filename': filename,
                            'url': audio_path
                        })
                        logging.error(f"下载失败: {filename}")
                    
                    # 添加小延迟，避免请求过于频繁
                    time.sleep(0.5)
                    
                except Exception as e:
                    failed_count += 1
                    logging.error(f"处理第{row_num}行时发生错误: {e}")
                    continue
    
    except FileNotFoundError:
        logging.error(f"CSV文件不存在: {csv_file}")
        return
    except Exception as e:
        logging.error(f"读取CSV文件时发生错误: {e}")
        return
    
    # 输出统计信息
    logging.info(f"\n下载完成!")
    logging.info(f"成功下载: {success_count} 个文件")
    logging.info(f"下载失败: {failed_count} 个文件")
    
    if failed_files:
        logging.info(f"\n失败的文件列表:")
        for failed in failed_files:
            logging.info(f"  行 {failed['row']}: {failed['filename']}")

def main():
    """主函数"""
    csv_file = "20250915_1042_zhb_sw_rand_Presto_SAFE_LYCC.csv"
    
    if not os.path.exists(csv_file):
        logging.error(f"CSV文件不存在: {csv_file}")
        return
    
    logging.info(f"开始处理CSV文件: {csv_file}")
    read_csv_and_download(csv_file)

if __name__ == "__main__":
    main()
