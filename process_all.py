#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键处理脚本：下载音频并拆分声道
"""

import os
import subprocess
import sys

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n{'='*50}")
    print(f"正在执行: {description}")
    print(f"命令: {command}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True, encoding='utf-8')
        print(result.stdout)
        if result.stderr:
            print("警告信息:")
            print(result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: 命令执行失败")
        print(f"返回码: {e.returncode}")
        print(f"错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    print("音频处理自动化脚本")
    print("功能: 1. 下载音频文件  2. 拆分双声道")
    
    # 检查CSV文件是否存在
    csv_file = "20250915_1042_zhb_sw_rand_Presto_SAFE_LYCC.csv"
    if not os.path.exists(csv_file):
        print(f"错误: CSV文件不存在: {csv_file}")
        return
    
    # 询问用户要执行的操作
    print("\n请选择要执行的操作:")
    print("1. 只下载音频文件")
    print("2. 只拆分现有音频文件的声道")
    print("3. 下载音频文件并拆分声道")
    print("4. 退出")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == "1":
        # 只下载
        success = run_command("python simple_download.py", "下载音频文件")
        if success:
            print("\n✅ 音频下载完成!")
        else:
            print("\n❌ 音频下载失败!")
            
    elif choice == "2":
        # 只拆分
        if not os.path.exists("audio_files"):
            print("错误: audio_files 文件夹不存在，请先下载音频文件")
            return
            
        success = run_command("python simple_split.py", "拆分音频声道")
        if success:
            print("\n✅ 音频拆分完成!")
        else:
            print("\n❌ 音频拆分失败!")
            
    elif choice == "3":
        # 下载并拆分
        print("\n开始完整处理流程...")
        
        # 步骤1: 下载音频
        success1 = run_command("python simple_download.py", "下载音频文件")
        if not success1:
            print("\n❌ 音频下载失败，停止处理")
            return
        
        print("\n✅ 音频下载完成!")
        
        # 步骤2: 拆分声道
        success2 = run_command("python simple_split.py", "拆分音频声道")
        if success2:
            print("\n✅ 完整处理流程完成!")
            print("\n📁 结果文件位置:")
            print("   - 原始音频: audio_files/公司名_录音文件名.wav")
            print("   - 左声道: audio_files/公司名_录音文件名_左声道.wav")
            print("   - 右声道: audio_files/公司名_录音文件名_右声道.wav")
        else:
            print("\n❌ 音频拆分失败!")
            
    elif choice == "4":
        print("退出程序")
        return
        
    else:
        print("无效选择，请重新运行程序")

if __name__ == "__main__":
    main()
