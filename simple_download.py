#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版音频下载程序
"""

import csv
import os
import requests
from urllib.parse import urlparse

def download_file(url, filename):
    """下载文件"""
    try:
        print(f"正在下载: {filename}")
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        
        print(f"下载完成: {filename}")
        return True
    except Exception as e:
        print(f"下载失败 {filename}: {e}")
        return False

def main():
    csv_file = "20250915_1042_zhb_sw_rand_Presto_SAFE_LYCC.csv"
    
    # 创建音频文件夹
    os.makedirs("audio_files", exist_ok=True)
    
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for i, row in enumerate(reader, 1):
            audio_path = row['audio_path']
            record_file = row['record_file']
            company_name = row['company_name']
            
            # 生成文件名
            filename = f"audio_files/{company_name}_{record_file}.wav"
            
            # 下载文件
            download_file(audio_path, filename)
            
            print(f"进度: {i}/101")

if __name__ == "__main__":
    main()
